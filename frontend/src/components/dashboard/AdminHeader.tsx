import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const AdminHeader: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  return (
    <header className="bg-white border-b border-gray-200 fixed top-0 left-0 right-0 z-50 h-16">
      <div className="px-4 lg:px-6 py-4 h-full">
        <div className="flex items-center justify-between h-full">
          <div className="flex items-center space-x-2 lg:space-x-4">
            <div className="w-8 h-8 bg-green-600 rounded flex items-center justify-center">
              <span className="text-white font-bold text-sm">🇰🇪</span>
            </div>
            <div className="hidden sm:block">
              <h2 className="font-semibold text-gray-900 text-sm lg:text-base">Kenya National Assembly</h2>
              <p className="text-xs lg:text-sm text-gray-500">Parliament Administration</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2 lg:space-x-4">
            <div className="text-right hidden sm:block">
              <p className="text-sm font-medium text-gray-900">{user?.name}</p>
              <p className="text-xs text-gray-500">{user?.level_display || user?.role_display}</p>
            </div>
            <button
              onClick={handleLogout}
              className="px-3 py-2 text-sm text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default AdminHeader;